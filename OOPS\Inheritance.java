// Superclass (Parent)
class Vehicle {
    protected String brand; // protected is accessible by subclasses

    public Vehicle(String brand) {
        this.brand = brand;
        System.out.println("Vehicle constructor called.");
    }

    public void move() {
        System.out.println("The vehicle is moving.");
    }
}

// Subclass (Child)
class Car extends Vehicle {
    private int numberOfDoors;

    public Car(String brand, int doors) {
        // 1. Call the parent constructor using super
        super(brand);
        this.numberOfDoors = doors;
        System.out.println("Car constructor called.");
    }

    // 2. Method Overriding
    @Override // Good practice to use this annotation
    public void move() {
        // 3. Optionally call the parent's method
        super.move();
        System.out.println("The car drives on four wheels.");
    }
}

public class Inheritance{
    public static void main(String args[]){
        Car car1=new Car("Chevrolet",6);
        System.out.println("Overridden Method: ");
        car1.move();
    }
}