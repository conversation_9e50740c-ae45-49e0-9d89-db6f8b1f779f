import java.util.Arrays;

public class Arrays {

    public static void main(String[] args) {

        // --- 1. Declaration and Initialization ---
        System.out.println("--- 1. Declaration and Initialization ---");
        int[] numbers; // Declaration
        numbers = new int[3]; // Initialization with 'new'
        numbers[0] = 10;
        numbers[1] = 20;
        numbers[2] = 30;

        System.out.println("Numbers array: " + Arrays.toString(numbers));
        System.out.println("The second element is: " + numbers[1]);

        String[] fruits = {"Apple", "Banana", "Cherry"}; // Initialization with literal
        System.out.println("Fruits array: " + Arrays.toString(fruits));
        System.out.println("Size of fruits array: " + fruits.length);
        System.out.println();

        // --- 2. Multidimensional Arrays ---
        System.out.println("--- 2. Multidimensional Arrays ---");
        int[][] matrix = new int[2][3];
        matrix[0][0] = 1;
        matrix[0][1] = 2;
        matrix[0][2] = 3;
        matrix[1][0] = 4;
        matrix[1][1] = 5;
        matrix[1][2] = 6;

        System.out.println("Matrix at [0][2]: " + matrix[0][2]);

        // Printing a 2D array is a bit different
        System.out.println("Printing the entire matrix:");
        for (int i = 0; i < matrix.length; i++) {
            System.out.println(Arrays.toString(matrix[i]));
        }
        System.out.println();

        // --- 3. Iteration Patterns ---
        System.out.println("--- 3. Iteration Patterns ---");
        
        // Standard for loop
        System.out.println("Standard for loop:");
        for (int i = 0; i < numbers.length; i++) {
            System.out.println("Element at index " + i + ": " + numbers[i]);
        }
        System.out.println();

        // Enhanced for loop (for-each)
        System.out.println("Enhanced for loop:");
        for (String fruit : fruits) {
            System.out.println("Fruit: " + fruit);
        }
        System.out.println();

        // Nested loops for 2D array
        System.out.println("Nested loops for matrix:");
        for (int i = 0; i < matrix.length; i++) {
            for (int j = 0; j < matrix[i].length; j++) {
                System.out.print(matrix[i][j] + " ");
            }
            System.out.println(); // New line for each row
        }
    }
}