// The "part" class
class Engine {
    public void start() {
        System.out.println("Engine has started.");
    }
}

// The "whole" class that is composed of parts
class Car {
    // Car "has-a" Engine - This is composition.
    private final Engine engine;

    public Car() {
        this.engine = new Engine(); // The Car creates its part.
    }

    // The Car delegates the start behavior to its engine part.
    public void startCar() {
        System.out.println("Car is starting...");
        engine.start();
    }
}

public class CompositionInheritance {
    public static void main(String[] args) {
        Car myCar = new Car();
        myCar.startCar();
    }
}
