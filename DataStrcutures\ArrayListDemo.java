import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
public class ArrayListDemo {
    public static void main(String[] args) {
        // Always use generics for type safety
        List<String> fruits = new ArrayList<>(); //fruits cannot acces ArrayList specific methods and always code to an interface and not an implementattion
        // 1. Add elements
        fruits.add("Apple");
        fruits.add("Banana");
        fruits.add("Cherry");
        System.out.println("Initial list: " + fruits); // [Apple, Banana, Cherry]
        System.out.println("Size: " + fruits.size());   // 3

        // 2. Get an element
        String secondFruit = fruits.get(2);
        System.out.println("Second fruit is: " + secondFruit); // Banana

        // 3. Set (replace) an element
        fruits.set(0, "Apricot");
        System.out.println("After replacing Apple: " + fruits); // [Apricot, Banana, Cherry]

        // Examples of different collections you can add:

        // 1. Arrays.asList() - converts array to List
        fruits.addAll(Arrays.asList("<PERSON><PERSON>","Mango","Peach"));

        /*// 2. Another ArrayList
        ArrayList<String> moreFruits = new ArrayList<>();
        moreFruits.add("Pineapple");
        moreFruits.add("Orange");
        fruits.addAll(moreFruits);

        // 3. HashSet (removes duplicates)
        Set<String> uniqueFruits = new HashSet<>();
        uniqueFruits.add("Strawberry");
        uniqueFruits.add("Blueberry");
        uniqueFruits.add("Apple"); // duplicate, won't be added again
        fruits.addAll(uniqueFruits);

        // 4. LinkedList
        LinkedList<String> exoticFruits = new LinkedList<>();
        exoticFruits.add("Dragon Fruit");
        exoticFruits.add("Passion Fruit");
        fruits.addAll(exoticFruits);

        // 5. List.of() - Java 9+ (immutable list)
        // fruits.addAll(List.of("Lemon", "Lime"));*/

        System.out.println("After adding all collections: " + fruits);

        // 4. Remove an element
        fruits.remove(2); // Removes "Cherry"
        System.out.println("After removing element at index 2: " + fruits); // [Apricot, Banana]

        System.out.println("\nIterating over the list:");
        // The best way to loop through a list
        for (String fruit : fruits) {
            System.out.println("- " + fruit);
        }
    }
}