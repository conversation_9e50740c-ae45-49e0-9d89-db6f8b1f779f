// Cannot create an instance of Animal, e.g., new Animal() is illegal.
abstract class Animal {
    // Concrete method: shared by all subclasses
    public void sleep() {
        System.out.println("This animal is sleeping.");
    }

    // Abstract method: MUST be implemented by subclasses
    public abstract void makeSound();
}

class Dog extends Animal {
    // Providing the implementation for the abstract method
    public void bark(){
        System.out.println("I'm barking!!");
    }
    @Override
    public void makeSound() {
        System.out.println("Woof");
    }
}

public class Abstraction{
    public static void main(String args[]){
        Dog laborador=new Dog();
        laborador.sleep();
        laborador.makeSound();
        laborador.bark();
    }
}