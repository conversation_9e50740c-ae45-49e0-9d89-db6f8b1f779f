import java.util.ArrayList;
import java.util.List; // Note the import

public class ArrayListDemo {
    public static void main(String[] args) {
        // Always use generics for type safety
        List<String> fruits = new ArrayList<>(); //fruits cannot acces ArrayList specific methods

        // 1. Add elements
        fruits.add("Apple");
        fruits.add("Banana");
        fruits.add("Cherry");
        System.out.println("Initial list: " + fruits); // [Apple, Banana, Cherry]
        System.out.println("Size: " + fruits.size());   // 3

        // 2. Get an element
        String secondFruit = fruits.get(1);
        System.out.println("Second fruit is: " + secondFruit); // Banana

        // 3. Set (replace) an element
        fruits.set(0, "Apricot");
        System.out.println("After replacing Apple: " + fruits); // [Apricot, Banana, Cherry]

        // 4. Remove an element
        fruits.remove(2); // Removes "Cherry"
        System.out.println("After removing element at index 2: " + fruits); // [Apricot, Banana]

        System.out.println("\nIterating over the list:");
        // The best way to loop through a list
        for (String fruit : fruits) {
            System.out.println("- " + fruit);
        }
    }
}