// Well-encapsulated Car class
class Car {
    private String model; // Data is hidden
    private int speed;

    public Car(String model) {
        this.model = model;
        this.speed = 0;
    }

    // Getter: public method to safely read the model
    public String getModel() {
        return this.model;
    }

    // Setter: public method to safely change the speed
    public void setSpeed(int speed) {
        if (speed >= 0 && speed <= 200) { // Validation logic
            this.speed = speed;
        } else {
            System.out.println("Invalid speed.");
        }
    }

    public int getSpeed() {
        return this.speed;
    }
}

public class Encapsulate {
    public static void main(String[] args) {
        Car myCar = new Car("Porsche 911");
        // System.out.println(myCar.model); // ERROR! Cannot access private field

        System.out.println("Model: " + myCar.getModel()); // Correct way to access
        myCar.setSpeed(60);
        System.out.println("Speed: " + myCar.getSpeed());

        myCar.setSpeed(300); // Fails validation
        System.out.println("Speed: " + myCar.getSpeed()); // Speed remains 60
    }
}