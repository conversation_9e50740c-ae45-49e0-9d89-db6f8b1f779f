// Blueprint
class Car {
    // State/Properties (Instance Variables)
    String color;
    String model;

    // Behavior (Method)
    void drive() {
        System.out.println(model + " is driving.");
    }
}

// Main class to run the code
public class ClassesObjects {
    public static void main(String[] args) {
        // Creating an object (instance) of the Car class
        Car myCar = new Car();
        myCar.model = "Tesla Model S"; // Setting properties
        myCar.drive(); // Calling a method
    }
}