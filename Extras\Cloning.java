// A mutable object to be used inside Person
class Address implements Cloneable {
    public String city;

    public Address(String city) {
        this.city = city;
    }

    // Overriding clone for Address
    @Override
    protected Object clone() throws CloneNotSupportedException {
        return super.clone();
    }
}

class Person implements Cloneable {
    public String name;
    public Address address; // A reference to another object

    public Person(String name, Address address) {
        this.name = name;
        this.address = address;
    }

    @Override
    public String toString() {
        return "Person[name=" + name + ", city=" + address.city + "]";
    }

    // Overriding clone() for a DEEP COPY
    @Override
    public Object clone() throws CloneNotSupportedException {
        // 1. Start with a shallow copy of the Person object
        Person clonedPerson = (Person) super.clone();

        // 2. Manually clone the mutable Address object
        clonedPerson.address = (Address) this.address.clone();

        return cloned<PERSON>erson;
    }
}

public class Cloning {
    public static void main(String[] args) throws CloneNotSupportedException {
        // Original Person object
        Person original = new Person("John", new Address("New York"));
        System.out.println("Original: " + original);

        // Create a clone
        Person clone = (Person) original.clone();
        System.out.println("Clone   : " + clone);

        System.out.println("\n--- Modifying the clone's city ---");
        clone.address.city = "Los Angeles";

        // With a deep copy, the original is unaffected.
        // If we had used a shallow copy, the original's city would also change!
        System.out.println("Clone   : " + clone);
        System.out.println("Original: " + original);
    }
}
