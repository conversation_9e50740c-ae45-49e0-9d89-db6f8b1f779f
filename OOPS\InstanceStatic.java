class Car {
    // Instance variable: each car has its own color
    String color;

    // Static variable: shared count for all car objects
    static int numberOfCars = 0;

    Car() {
        numberOfCars++; // Increment the shared counter
    }
}

public class InstanceStatic {
    public static void main(String[] args) {
        System.out.println("Initial cars: " + Car.numberOfCars); // Access via class name

        Car car1 = new Car();
        car1.color = "Red";

        Car car2 = new Car();
        car2.color = "Blue";

        // Both objects share the same static variable
        System.out.println("Cars created: " + Car.numberOfCars); // Output: 2
    }
}
