import java.util.ArrayList;
import java.util.Arrays;
abstract class Vehicle{
    protected int vehicleId;
    protected String brand;
    protected double rentalPricePerDay;

    public Vehicle(int vehicleId,String brand,double rentalPricePerDay){
        this.vehicleId=vehicleId;
        this.brand=brand;
        this.rentalPricePerDay=rentalPricePerDay;
    }

    public abstract void displayDetails();

}


class Car extends Vehicle{
    private int seatingCapacity;
    private String fuelType;

    public Car(int vehicleId,String brand,double rentalPricePerDay,int seatingCapacity,String fuelType){
        super(vehicleId,brand,rentalPricePerDay);
        this.seatingCapacity=seatingCapacity;
        this.fuelType=fuelType;
    }

    @Override
    public void displayDetails(){
        System.out.println("I'm a Car");
        System.out.println("Vehicle ID : "+this.vehicleId);
        System.out.println("Brand : "+this.brand);
        System.out.println("Rental Price Per Day : "+this.rentalPricePerDay);
        System.out.println("Seating Capacity : "+this.seatingCapacity);
        System.out.println("Fuel Type : "+this.fuelType);
    }
}
class Bike extends Vehicle{
    private int engineCapacity;
    private String type;

    public Bike(int vehicleId,String brand,double rentalPricePerDay,int engineCapacity,String type){
        super(vehicleId,brand,rentalPricePerDay);
        this.engineCapacity=engineCapacity;
        this.type=type;
    }

    @Override
    public void displayDetails(){
        System.out.println("I'm a Bike");
        System.out.println("Vehicle ID : "+this.vehicleId);
        System.out.println("Brand : "+this.brand);
        System.out.println("Rental Price Per Day : "+this.rentalPricePerDay);
        System.out.println("Engine Capacity : "+this.engineCapacity);
        System.out.println("Type : "+this.type);
    }


}
class Truck extends Vehicle{
    private int loadCapacity;
    private int numberofAxles;

    public Truck(int vehicleId,String brand,double rentalPricePerDay,int loadCapacity,int numberofAxles){
        super(vehicleId,brand,rentalPricePerDay);
        this.loadCapacity=loadCapacity;
        this.numberofAxles=numberofAxles;
    }

    @Override
    public void displayDetails(){
        System.out.println("I'm a Truck");
        System.out.println("Vehicle ID : "+this.vehicleId);
        System.out.println("Brand : "+this.brand);
        System.out.println("Rental Price Per Day : "+this.rentalPricePerDay);
        System.out.println("Load Capacity : "+this.loadCapacity);
        System.out.println("No. of Axle's : "+this.numberofAxles);
    }
}
class RentalAgency{
    private ArrayList<Vehicle> vehicles = new ArrayList<>();

    public void addVehicle(Vehicle v){
        vehicles.add(v);

    }
    public void displayAllVehicles(){
        for(Vehicle v:vehicles){
            System.out.println(v);
        }
    }
    public ArrayList<Vehicle> searchVehicleByBrand(String brand){
        ArrayList<Vehicle> temp=new ArrayList<>();
        for(Vehicle v:vehicles){
            if(v.brand==brand){
                temp.add(v);
            }

        }
        return temp;

    }
    public double calculateRentalCost(int vehicleId,int days){
        for(Vehicle v:vehicles){
            if(v.vehicleId==vehicleId){
                return v.rentalPricePerDay*days;
            }

        }

    }
}

public class VehicleRentalSystem {
    public static void main(String[] args) {
        Vehicle car1=new Car(5638535,"Volvo",19000,6,"Diesel");
        Vehicle bike1=new Bike(4964974,"BMW",28000,750,"Petrol");
        Vehicle truck1=new Truck(9680530,"Eicher",25000,600,12);
        car1.displayDetails();
        bike1.displayDetails();
        truck1.displayDetails();

        RentalAgency agen=new RentalAgency();
        agen.add(Arrays.asList(car1,bike1,truck1));

    }
    
}
