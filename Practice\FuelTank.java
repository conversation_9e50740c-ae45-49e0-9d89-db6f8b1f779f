import java.lang.*;
import java.util.*;
// package Practice;

public class FuelTank{
    private double capacity;
    private double currentLevel;
    private double fillRate;
    private double drainRate;

    public FuelTank(float capacity,float currentLevel, double fillRate, double drainRate){
        this.capacity=capacity; //lts
        this.currentLevel=currentLevel;
        this.fillRate=fillRate; //lts per minute
        this.drainRate=drainRate; //lts per minute
    }

    public double fillTank(){
        return (this.capacity - this.currentLevel);
    }

    public double fillTime(){
        return (this.capacity-this.currentLevel)/this.fillRate;
    }
    
    public double drainTime(){
        return this.currentLevel/this.drainRate;
    }

    public String addFuel(int minutes){
        if(this.currentLevel==this.capacity){
            return "Tank is already full";
        }
        if(this.currentLevel+(this.fillRate*minutes) > this.capacity){
            this.currentLevel=this.capacity;
            return "Cannot fill more than " + this.capacity + "Litres , Current Fuel Level : " + this.currentLevel;
        }
        this.currentLevel=this.currentLevel+(this.fillRate*minutes);
        return "Current Fuel Level : " + this.currentLevel;
    }

    public String drainFuel(int minutes){
        if(this.currentLevel==0){
            return "Tank is empty";
        }
        if(this.currentLevel-(this.drainRate*minutes) < 0){
            this.currentLevel=0.0f;
            return "Cannot empty below 0 Litres , Current Fuel Level : " + this.currentLevel;
        }
        this.currentLevel=this.currentLevel-(this.drainRate*minutes);
        return "Current Fuel Level : " + this.currentLevel;
        

    }



    public static void main(String[] args) {
        Scanner scanner=new Scanner(System.in);
        FuelTank ft=new FuelTank(30,10,8,5);
        int op;
        do { 
            System.out.println("Operations");
            System.out.println("1.Check fuel needed to fill");
            System.out.println("2.Time to fill");
            System.out.println("3.Time to empty");
            System.out.println("4.Add fuel for a specific amount of time");
            System.out.println("5.Drain fuel for a specific amount of time");
            System.out.println("6.Exit");
            op=scanner.nextInt();
            scanner.nextLine();

            switch(op){
                case 1:
                    System.out.println(ft.fillTank());
                    break;
                case 2:
                    System.out.println(ft.fillTime());
                    break;
                case 3:
                    System.out.println(ft.drainTime());
                    break;
                case 4:
                    int fillMinutes=scanner.nextInt();
                    System.out.println(ft.addFuel(fillMinutes));
                    scanner.nextLine();
                    break;
                case 5:
                    int drainMinutes=scanner.nextInt();
                    System.out.println(ft.drainFuel(drainMinutes));
                    scanner.nextLine();
                    break;
                case 6:
                    System.out.println("Exiting...");
                    break;
                default:
                    System.out.println("Invalid Operation");
            }
            
        } while (op!=6);

        scanner.close();
        

    }

}

