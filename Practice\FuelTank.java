import java.util.Scanner;
import java.lang.*;
// package Practice;

public class FuelTank{
    private float capacity;
    private float currentLevel;
    public double fillRate;
    public double drainRate;

    public FuelTank(capacity,fillRate,drainRate){
        this.capacity=capacity; //lts
        this.currentLevel=0.0;
        this.fillRate=fillRate; //lts per minute
        this.drainRate=drainRate; //lts per minute
    }

    public float fillTank(){
        return (capacity - currentLevel) + "Litres";
    }


    public static void main(String[] args) {
        FuelTank ft=new FuelTank(30,8,5);
        System.out.println(ft.fillTank());

    }

}

