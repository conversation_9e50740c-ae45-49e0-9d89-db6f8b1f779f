class Car {
    String model;
    int year;

    // 1. Default Constructor
    public Car() {
        // this.model = "Unknown";
        // this.year = 2025;\
        this("Unknown",2025);  //calling another constructor using 'this'
    }

    // 2. Parameterized Constructor
    public Car(String modelName) {
        this.model = modelName;
        this.year = 2025;
    }

    // 3. Overloaded Parameterized Constructor
    public Car(String model, int year) {
        this.model = model;
        this.year = year;
    }
}
public class ThisConstructor{
    public static void main(String args[]){
        Car car1=new Car();
        System.out.println(car1.model+ " " +car1.year);
        Car car2=new Car("Ferrari F5");
        System.out.println(car2.model+ " " +car2.year);
        Car car3=new Car("Chrysler",2008);
        System.out.println(car3.model+ " " +car3.year);

    }
}