class Vehicle {
    protected String brand = "Generic";

    public void startEngine() {
        System.out.println("Vehicle engine starts.");
    }
}

class Car extends Vehicle {
    public Car() {
        this.brand = "Ford";
    }

    @Override
    public void startEngine() {
        System.out.println("Car engine starts with a roar.");
    }

    public void openTrunk() {
        System.out.println("Trunk is open.");
    }
}

class Motorcycle extends Vehicle {
    public Motorcycle() {
        this.brand = "Honda";
    }



    @Override
    public void startEngine() {
        System.out.println("Motorcycle engine starts with a rumble.");
    }
}

public class Polymorphism {
    public static void main(String[] args) {
        // A superclass reference can hold a subclass object
        Vehicle myVehicle1 = new Car();         // A Car IS A Vehicle
        Vehicle myVehicle2 = new Motorcycle();  // A Motorcycle IS A Vehicle

        System.out.println("--- Testing Runtime Polymorphism ---");
        // The JVM checks the actual object type at runtime to call the correct method
        myVehicle1.startEngine(); // Calls the overridden method in Car
        myVehicle2.startEngine(); // Calls the overridden method in Motorcycle

        System.out.println("\n--- Testing instanceof ---");
        // We can't call openTrunk() on myVehicle1 directly because the compiler only sees a Vehicle reference.
        // if (myVehicle1.openTrunk()) { } // COMPILE ERROR!

        // Use instanceof to check the type before casting
        if (myVehicle1 instanceof Car) {
            // We cast the reference to a Car to access Car-specific methods
            Car myCar = (Car) myVehicle1;
            myCar.openTrunk();
        }
    }
}