// A contract for things that can be resized
interface Resizable {
    void resize(double factor); // public and abstract by default
}

// A contract for things that can be drawn
interface Drawable {
    void draw();
}

// This class fulfills two separate contracts
class Circle implements Resizable, Drawable {
    private double radius;

    public Circle(double radius) { this.radius = radius; }

    @Override
    public void resize(double factor) {
        this.radius *= factor;
    }

    @Override
    public void draw() {
        System.out.println("Drawing a circle with radius " + this.radius);
    }
}

public class Interface{
    public static void main(String args[]){
        Circle circ1=new Circle(2.5);
        circ1.resize(4);
        circ1.draw();

    }
}