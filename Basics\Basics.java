import java.util.Scanner; // Needed for Scanner
import java.io.BufferedReader; // Needed for BufferedReader
import java.io.InputStreamReader; // Needed for BufferedReader

public class Basics {

    // The main method - your program's starting point
    public static void main(String[] args) throws Exception { // Added 'throws Exception' for BufferedReader simplicity

        // --- 1. Variables and Data Types ---
        System.out.println("--- Variables and Data Types ---");
        int studentAge = 20;
        double studentGPA = 3.85;
        boolean isEnrolled = true;
        char initial = 'J';
        String studentName = "Alice Wonderland";

        System.out.println("Name: " + studentName);
        System.out.println("Age: " + studentAge);
        System.out.println("GPA: " + studentGPA);
        System.out.println("Enrolled: " + isEnrolled);
        System.out.println("Initial: " + initial);
        System.out.println(); // Blank line for readability

        // --- 2. Control Flow: if-else if-else ---
        System.out.println("--- Control Flow: if-else if-else ---");
        int score = 75;
        if (score >= 90) {
            System.out.println("Grade: A");
        } else if (score >= 80) {
            System.out.println("Grade: B");
        } else if (score >= 70) {
            System.out.println("Grade: C");
        } else {
            System.out.println("Grade: F");
        }
        System.out.println();

        // --- 3. Control Flow: switch ---
        System.out.println("--- Control Flow: switch ---");
        String dayOfWeek = "Wednesday";
        switch (dayOfWeek) {
            case "Monday":
                System.out.println("Start of the week!");
                break; // Important to break!
            case "Wednesday":
                System.out.println("Hump day!");
                break;
            case "Friday":
                System.out.println("Almost weekend!");
                break;
            default:
                System.out.println("Just another day.");
        }
        System.out.println();

        // --- 4. Control Flow: for loop ---
        System.out.println("--- Control Flow: for loop (Counting 1 to 5) ---");
        for (int i = 1; i <= 5; i++) {
            System.out.print(i + " ");
        }
        System.out.println("\n");

        // --- 5. Control Flow: while loop ---
        System.out.println("--- Control Flow: while loop (Countdown from 3) ---");
        int count = 3;
        while (count > 0) {
            System.out.println("Countdown: " + count);
            count--;
        }
        System.out.println("Lift off!\n");

        // --- 6. Control Flow: do-while loop ---
        System.out.println("--- Control Flow: do-while loop (Guessing game - enter 5) ---");
        Scanner scannerForDoWhile = new Scanner(System.in);
        int guess;
        do {
            System.out.print("Guess the number (it's 5): ");
            guess = scannerForDoWhile.nextInt();
        } while (guess != 5);
        System.out.println("You got it!\n");
        scannerForDoWhile.close(); // Close scanner when done

        // --- 7. Type Casting ---
        System.out.println("--- Type Casting ---");
        // Implicit casting (int to double)
        int myInt = 100;
        double myDouble = myInt; // Automatic
        System.out.println("Implicit cast (int to double): " + myDouble);

        // Explicit casting (double to int - potential data loss)
        double anotherDouble = 99.99;
        int anotherInt = (int) anotherDouble; // Manual cast
        System.out.println("Explicit cast (double to int): " + anotherInt + " (decimal truncated)");

        // Explicit casting (char to int - ASCII value)
        char myChar = 'A';
        int asciiValue = (int) myChar;
        System.out.println("Explicit cast (char to int - ASCII): " + asciiValue + "\n");

        // --- 8. Jump Statements: break and continue ---
        System.out.println("--- Jump Statements: break and continue ---");
        for (int i = 1; i <= 10; i++) {
            if (i == 3) {
                System.out.println("Skipping 3 (continue)");
                continue; // Skips the rest of this iteration
            }
            if (i == 7) {
                System.out.println("Breaking at 7");
                break; // Exits the loop entirely
            }
            System.out.println("Current number: " + i);
        }
        System.out.println();

        // --- 9. Reading Input: Scanner vs. BufferedReader ---

        // Using Scanner (simple for general use)
        System.out.println("--- Reading Input: Scanner ---");
        Scanner scanner = new Scanner(System.in); // Creates a Scanner object
        System.out.print("Enter your favorite fruit (Scanner): ");
        String fruit = scanner.nextLine();
        System.out.println("You like: " + fruit);
        System.out.print("Enter a number (Scanner): ");
        int number = scanner.nextInt();
        System.out.println("You entered: " + number);
        scanner.nextLine(); // Consume the leftover newline character after nextInt()
        System.out.println();
        scanner.close(); // Very important to close resources!

        // Using BufferedReader (more efficient for competitive programming / large inputs)
        System.out.println("--- Reading Input: BufferedReader ---");
        // No 'throws Exception' for main in real projects; use try-catch
        BufferedReader reader = new BufferedReader(new InputStreamReader(System.in));
        System.out.print("Enter your city (BufferedReader): ");
        String city = reader.readLine(); // Reads a whole line
        System.out.println("Your city is: " + city);
        // For numbers with BufferedReader, you often read as String and parse
        System.out.print("Enter another number (BufferedReader): ");
        String numStr = reader.readLine();
        int anotherNumber = Integer.parseInt(numStr); // Convert String to int
        System.out.println("You entered: " + anotherNumber);
        System.out.println();
        reader.close(); // Close resources!

        // --- 10. `return` in a simple method ---
        System.out.println("--- Using 'return' in a method ---");
        int resultSum = addNumbers(5, 7);
        System.out.println("Sum of 5 and 7: " + resultSum);
        System.out.println("Is 10 even? " + isEven(10));
        System.out.println("Is 9 even? " + isEven(9));
    }

    // A simple method to demonstrate 'return'
    public static int addNumbers(int a, int b) {
        return a + b; // Returns the sum of a and b
    }

    public static boolean isEven(int num) {
        if (num % 2 == 0) {
            return true; // If true, exit immediately
        }
        return false; // Otherwise, return false
    }
}